# 📊 **SENTRYCOIN v6.1 "PROJECT FORTRESS" - DATA DICTIONARY**

## **CLASSIFICATION: TECHNICAL REFERENCE**

**PURPOSE:** Authoritative definition for every metric, log key, and data structure generated by the SentryCoin v6.1 system. This document ensures complete auditability and transparency of all system outputs.

---

## 🔍 **LOG TYPES**

### **DIAGNOSTIC**
**Definition:** High-fidelity decision-making pipeline log generated by the MarketClassifier on every tick.
**Frequency:** Every market data update (stateful - only logged when regime or reason changes)
**Structure:**
```json
{
  "logType": "DIAGNOSTIC",
  "timestamp": "ISO 8601 timestamp",
  "symbol": "Trading pair symbol",
  "inputs": {
    "price": "Current market price (float, 2 decimals)",
    "dlsScore": "Dynamic Liquidity Score (float, 1 decimal, 0-100)",
    "pressure": "Ask/Bid pressure ratio (float, 2 decimals)",
    "momentum": "Price momentum percentage (float, 3 decimals)"
  },
  "classifierOutput": {
    "regime": "Detected market regime (CASCADE_HUNTER|COIL_WATCHER|SHAKEOUT_DETECTOR|NO_REGIME)",
    "reason": "Human-readable explanation for regime classification",
    "checks": {
      "CASCADE": "Condition check result (PASS|FAIL with failure reasons)",
      "COIL": "Condition check result (PASS|FAIL with failure reasons)",
      "SHAKEOUT": "Condition check result (PASS|FAIL with failure reasons)"
    }
  }
}
```

### **HEARTBEAT**
**Definition:** Engine status confirmation emitted every 60 seconds to verify main loop operation.
**Frequency:** Every 60 seconds
**Structure:**
```json
{
  "logType": "HEARTBEAT",
  "status": "Engine operational status (OPERATIONAL|OFFLINE)",
  "activeStrategies": ["Array of active strategy names"],
  "ethUnwindState": "ETH_UNWIND strategy state (MONITORING|ARMED|EXECUTING)",
  "activePositions": "Number of open trading positions (integer)",
  "timestamp": "ISO 8601 timestamp",
  "uptime": "Engine uptime in seconds (integer)",
  "systemHealth": "Object containing component health status"
}
```

### **WHALE_MEMPOOL_TX**
**Definition:** Structured log for every mempool transaction involving a monitored whale address.
**Frequency:** Real-time (whenever whale transaction detected)
**Structure:**
```json
{
  "logType": "WHALE_MEMPOOL_TX",
  "whaleAddress": "Ethereum address of the whale",
  "transactionHash": "Transaction hash",
  "from": "Sender address",
  "to": "Recipient address",
  "valueEth": "Transaction value in ETH (float, 4 decimals)",
  "valueUSD": "Estimated USD value (integer)",
  "isNew": "Boolean indicating if this is a new transaction detection",
  "provider": "Data provider (alchemy|blocknative|quicknode)",
  "timestamp": "ISO 8601 timestamp",
  "detectionLatency": "Time from system start to detection (milliseconds)"
}
```

---

## 📈 **PERFORMANCE METRICS**

### **WhaleIntents**
**Definition:** Total number of whale intent events detected since system start.
**Type:** Cumulative counter (integer)
**Source:** PhoenixEngine.metrics.whaleIntentsDetected
**Reset:** System restart only

### **LiqDetections**
**Definition:** Total number of liquidity analysis operations performed by the Dynamic Liquidity Analyzer.
**Type:** Cumulative counter (integer)
**Source:** PhoenixEngine.metrics.liquidityValidations
**Reset:** System restart only

### **SigDetections**
**Definition:** Total number of trading signals generated by the market classifier.
**Type:** Cumulative counter (integer)
**Source:** MarketClassifier signal emission events
**Reset:** System restart only

### **Tasks**
**Definition:** Total number of tasks executed by the microservice task scheduler.
**Type:** Cumulative counter (integer)
**Source:** PhoenixEngine.metrics.tasksExecuted
**Reset:** System restart only

### **OI_Delta_1m**
**Definition:** Open Interest change rate over the last minute, expressed in millions of USD.
**Type:** Rate measurement (string with +/- prefix and M suffix)
**Source:** DerivativesMonitor.data.openInterest.changeRate
**Example:** "+1.2M" (increase of $1.2M per minute), "-0.8M" (decrease of $0.8M per minute)
**Reset:** Continuous calculation

### **Funding_Rate**
**Definition:** Current perpetual futures funding rate as a percentage.
**Type:** Rate measurement (string with % suffix)
**Source:** DerivativesMonitor.data.fundingRates.current
**Example:** "0.015%" (positive funding rate), "-0.008%" (negative funding rate)
**Reset:** Real-time update from exchange feeds

---

## 🏥 **SYSTEM HEALTH INDICATORS**

### **Component Health States**
**ONLINE:** Component is fully operational and processing data
**OFFLINE:** Component is not running or has failed initialization
**LIMITED:** Component is running but with reduced functionality (e.g., missing API keys)
**ERROR:** Component has encountered a critical error and requires attention

### **Worker Exit Codes**
**0:** Clean shutdown - worker received shutdown signal and terminated gracefully
**1:** Error exit - worker encountered an unhandled exception or critical error
**Non-zero (other):** Abnormal termination - indicates system instability

---

## 🔧 **TASK SCHEDULER METRICS**

### **task_retry_scheduled**
**Definition:** Warning log indicating a task failed and is being retried.
**Required Fields:**
- `taskId`: Unique identifier for the failed task
- `retryCount`: Current retry attempt number
- `nextAttempt`: Timestamp when retry will be executed
- `originalError`: Error message that caused the initial failure
- `taskType`: Type of task that failed

### **worker_terminated_cleanly**
**Definition:** Confirmation that a worker process exited with code 0.
**Required Fields:**
- `workerId`: Unique identifier for the worker
- `exitCode`: Should always be 0 for this log type

### **worker_terminated_error**
**Definition:** Alert that a worker process exited with a non-zero code.
**Required Fields:**
- `workerId`: Unique identifier for the worker
- `exitCode`: Non-zero exit code indicating the type of failure

---

## 🎯 **CLASSIFICATION THRESHOLDS**

### **Dynamic Liquidity Score (DLS) Thresholds**
- **Signal Validation:** 75th percentile (minimum for any signal consideration)
- **High Confidence:** 90th percentile (premium signal threshold)
- **COIL Detection:** 85th percentile (accumulation phase threshold)
- **SHAKEOUT Detection:** 80th percentile (stop hunt detection threshold)

### **Market Regime Conditions**
**CASCADE_HUNTER:**
- Pressure: ≥3.0x (ask/bid ratio)
- Liquidity: ≥75th percentile DLS
- Momentum: ≤-0.3% (strong negative)

**COIL_WATCHER:**
- Pressure: ≤2.0x (low pressure)
- Liquidity: ≥85th percentile DLS
- Momentum: -0.1% to +0.1% (neutral)

**SHAKEOUT_DETECTOR:**
- Pressure: ≤1.5x (very low pressure)
- Liquidity: ≥80th percentile DLS
- Momentum: ≤-0.5% (extreme negative)

---

## 🚨 **ERROR CLASSIFICATIONS**

### **CRITICAL:** System-threatening errors requiring immediate attention
### **ERROR:** Component failures that impact functionality
### **WARN:** Potential issues that should be monitored
### **INFO:** Normal operational events
### **DEBUG:** Detailed diagnostic information

---

**Last Updated:** July 26, 2025  
**Version:** 6.1.0 "Project Fortress"  
**Maintainer:** Chief Architect & Site Reliability Engineer
