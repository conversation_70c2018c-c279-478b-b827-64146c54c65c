# ================================================================================
# PHOENIX ENGINE v6.1 "PROJECT FORTRESS" - AUTHORITATIVE CONFIGURATION
# ================================================================================
# Clean, unambiguous configuration for Phoenix Engine v6.1
# All legacy v4.x parameters removed - only active parameters included
# Last Updated: July 26, 2025 - Project Fortress Implementation
# ================================================================================

# ================================================================================
# CORE SYSTEM CONFIGURATION
# ================================================================================

# Trading Symbol (Primary market for analysis)
SYMBOL=ETHUSDT

# Trading Mode (PAPER for simulation, LIVE for real capital)
PAPER_TRADING=true

# Real-time Data Feeds (true/false)
ENABLE_REAL_TIME_FEEDS=true

# System Environment (development/production)
NODE_ENV=production

# ================================================================================
# TELEGRAM NOTIFICATIONS (REQUIRED)
# ================================================================================

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_ID=**********
TELEGRAM_ADMIN_CHAT_ID=**********
TELEGRAM_API_ID=29395164
TELEGRAM_API_HASH=4fd72e3993e581776c5aabd3c88771cc

# ================================================================================
# BLOCKCHAIN DATA PROVIDERS (REQUIRED)
# ================================================================================

# Etherscan API (Required for blockchain data)
ETHERSCAN_API_KEY=**********************************

# Alpha Vantage API (Optional - for additional market data)
ALPHA_VANTAGE_API_KEY=GYFR9H9CE8EUFHP2

# ================================================================================
# MEMPOOL STREAMING PROVIDERS (OPTIONAL)
# ================================================================================

# Alchemy (Primary mempool provider)
ALCHEMY_API_KEY=bk_VgLibn1MPpxLud_4Ve
ALCHEMY_NETWORK_URL=https://eth-mainnet.g.alchemy.com/v2/bk_VgLibn1MPpxLud_4Ve
ALCHEMY_WS_URL=wss://eth-mainnet.g.alchemy.com/v2/bk_VgLibn1MPpxLud_4Ve

# QuickNode (Backup mempool provider)
QUICKNODE_WS_URL=wss://your-quicknode-endpoint.quiknode.pro/your-key/

# ================================================================================
# DERIVATIVES MONITORING (OPTIONAL)
# ================================================================================

# Bybit API (Optional - for private derivatives data)
BYBIT_API_KEY=your_bybit_api_key_here
BYBIT_SECRET=your_bybit_secret_here

# ================================================================================
# WHALE MONITORING CONFIGURATION
# ================================================================================

# Whale Address Watchlist (comma-separated)
WHALE_WATCHLIST=******************************************,******************************************,******************************************

# Whale Transaction Threshold (minimum value in ETH)
WHALE_THRESHOLD=100

# ================================================================================
# PHOENIX ENGINE PERFORMANCE TUNING
# ================================================================================

# Task Scheduler Configuration
MAX_WORKERS=8
TASK_QUEUE_SIZE=1000
WORKER_TIMEOUT=30000

# Logging Configuration
LOG_LEVEL=info
LOG_RETENTION_DAYS=7

# Health Check Intervals (milliseconds)
HEALTH_CHECK_INTERVAL=30000
PERFORMANCE_METRICS_INTERVAL=300000

# ================================================================================
# RENDER DEPLOYMENT CONFIGURATION
# ================================================================================

# Port (automatically set by Render)
PORT=10000

# ================================================================================
# END OF PHOENIX v6.0 CONFIGURATION
# ================================================================================